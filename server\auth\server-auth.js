/**
 * 服务器认证模块 - 完整拆分版本
 * 包含所有认证相关的API和功能
 * 对应原始文件第298-322行的完整内容，包含以下API：
 * - POST /api/auth/login - 用户登录
 * - GET /api/auth/verify - 验证令牌
 * - GET /api/test/throttle-log - 测试节流日志
 * - GET /api/test - 测试API
 * 以及所有相关的认证和测试功能
 */

const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');

// JWT密钥（应该从环境变量获取）
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

// 认证模块设置函数
async function setupServerAuth(app, io, coreData, authData) {
  console.log('🔧 设置认证模块...');
  
  const { 
    pool,
    devices, 
    webClients,
    logs,
    pendingCommands,
    deviceCommands,
    throttledLog
  } = coreData;

  const { authenticateToken } = authData;

  // 用户登录API (原始文件第298行) - 已升级为JWT认证
  app.post('/api/auth/login', async (req, res) => {
    try {
      const { username, password } = req.body;

      console.log('登录尝试:', { username, password: '***' });

      if (!username || !password) {
        return res.status(400).json({
          success: false,
          message: '用户名和密码不能为空'
        });
      }

      // 查询用户信息
      console.log('查询用户:', username);
      const [users] = await pool.execute(
        'SELECT id, username, password, email, role, is_active FROM users WHERE username = ?',
        [username]
      );

      console.log('查询结果:', users.length, '个用户');
      if (users.length > 0) {
        console.log('用户信息:', {
          id: users[0].id,
          username: users[0].username,
          password: users[0].password,
          is_active: users[0].is_active
        });
      }

      if (users.length === 0) {
        console.log('用户不存在:', username);
        return res.status(401).json({
          success: false,
          message: '用户名或密码错误'
        });
      }

      const user = users[0];

      // 检查用户是否激活
      if (!user.is_active) {
        return res.status(401).json({
          success: false,
          message: '账户已被禁用'
        });
      }

      // 验证密码（支持明文密码和bcrypt加密密码）
      let passwordValid = false;

      if (user.password.startsWith('$2b$') || user.password.startsWith('$2a$')) {
        // bcrypt加密密码
        console.log('使用bcrypt验证密码');
        passwordValid = await bcrypt.compare(password, user.password);
      } else {
        // 明文密码（向后兼容）
        console.log('使用明文密码验证');
        passwordValid = password === user.password;
      }

      console.log('密码验证结果:', passwordValid);

      if (!passwordValid) {
        console.log('密码验证失败');
        return res.status(401).json({
          success: false,
          message: '用户名或密码错误'
        });
      }

      // 生成JWT token
      const tokenPayload = {
        userId: user.id,
        username: user.username,
        email: user.email,
        role: user.role
      };

      const token = jwt.sign(tokenPayload, JWT_SECRET, { expiresIn: '24h' });

      // 更新最后登录时间
      await pool.execute(
        'UPDATE users SET last_login_time = NOW(), login_count = COALESCE(login_count, 0) + 1 WHERE id = ?',
        [user.id]
      );

      console.log(`用户 ${username} 登录成功，生成JWT token`);

      res.json({
        success: true,
        message: '登录成功',
        token: token,
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          role: user.role
        }
      });
    } catch (error) {
      console.error('登录失败:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  });

  // 验证令牌API (原始文件第313行)
  app.get('/api/auth/verify', authenticateToken, (req, res) => {
    res.json({
      success: true,
      message: '令牌有效',
      user: req.user
    });
  });

  // 测试节流日志功能的接口 (原始文件第322行)
  app.get('/api/test/throttle-log', (req, res) => {
    const testKey = 'test_throttle';
    const testMessage = `测试节流日志 - ${new Date().toLocaleTimeString()}`;
    
    // 测试节流日志功能
    const logged = throttledLog(testKey, testMessage);
    
    res.json({
      success: true,
      message: '节流日志测试完成',
      data: {
        key: testKey,
        message: testMessage,
        logged: logged,
        timestamp: new Date().toISOString()
      }
    });
  });

  // 通用测试API (原始文件第10786行)
  app.get('/api/test', (req, res) => {
    res.json({
      success: true,
      message: 'API测试成功',
      timestamp: new Date().toISOString(),
      server: {
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        platform: process.platform,
        nodeVersion: process.version
      },
      statistics: {
        connectedDevices: devices.size,
        webClients: webClients.size,
        pendingCommands: Array.from(pendingCommands.values()).reduce((sum, cmds) => sum + cmds.length, 0),
        totalLogs: logs.length
      }
    });
  });

  console.log('✅ 认证模块设置完成');

  // 返回认证相关函数供其他模块使用
  return {
    // 可以在这里返回一些认证相关的工具函数
  };
}

module.exports = { setupServerAuth };
